import os
import json
from llm_client import DeepSeek<PERSON>lient
from exception_script_templates import ScriptTemplates

class ExceptionScriptGenerator:
    def __init__(self):
        self.llm_client = DeepSeekClient()
        self.function_scores_path = "tool/function_scores.json"
        self.risks_input_dir = "generator/function_risks"
        self.script_output_dir = "generated_scripts"

        # 确保输出目录存在
        os.makedirs(self.script_output_dir, exist_ok=True)
        
        # TPCH数据库模式
        self.tpch_schema = """
TPC-H数据库模式包含以下表：

REGION (R_REGIONKEY, R_NAME, R_COMMENT)
NATION (N_NATIONKEY, N_NAME, N_REGIONKEY, N_COMMENT)
SUPPLIER (S_SUPPKEY, S_NAME, S_ADDRESS, S_NATIONKEY, S_PHONE, S_ACCTBAL, S_COMMENT)
CUSTOMER (C_CUSTKEY, C_NAME, C_ADDRESS, C_NATIONKEY, C_PHONE, C_ACCTBAL, C_MKTSEGMENT, C_COMMENT)
PART (P_PARTKEY, P_NAME, P_MFGR, P_BRAND, P_TYPE, P_SIZE, P_CONTAINER, P_RETAILPRICE, P_COMMENT)
PARTSUPP (PS_PARTKEY, PS_SUPPKEY, PS_AVAILQTY, PS_SUPPLYCOST, PS_COMMENT)
ORDERS (O_ORDERKEY, O_CUSTKEY, O_ORDERSTATUS, O_TOTALPRICE, O_ORDERDATE, O_ORDERPRIORITY, O_CLERK, O_SHIPPRIORITY, O_COMMENT)
LINEITEM (L_ORDERKEY, L_PARTKEY, L_SUPPKEY, L_LINENUMBER, L_QUANTITY, L_EXTENDEDPRICE, L_DISCOUNT, L_TAX, L_RETURNFLAG, L_LINESTATUS, L_SHIPDATE, L_COMMITDATE, L_RECEIPTDATE, L_SHIPINSTRUCT, L_SHIPMODE, L_COMMENT)

"""
    
    def load_function_scores(self):

        with open(self.function_scores_path, 'r') as f:
            return json.load(f)
    
    def load_function_risks(self, function_name):

        risk_file = os.path.join(self.risks_input_dir, f"{function_name}.txt")
        if not os.path.exists(risk_file):
            return []
        
        with open(risk_file, 'r', encoding='utf-8') as f:
            return [line.strip() for line in f.readlines() if line.strip()]
    
    def generate_exception_script_for_function(self, function_name, risks):
        """为指定函数生成能够触发异常的Python脚本"""
        if not risks:
            return None

        risks_text = '\n'.join([f"- {risk}" for risk in risks])

        # 获取基础模板
        base_template = ScriptTemplates.get_base_template()

        prompt = f"""
基于以下SQLite内核函数的性能风险点，生成一个完整的Python脚本，该脚本能够触发该函数的异常或性能问题。

函数名: {function_name}

已识别的性能风险点:
{risks_text}

数据库模式:
{self.tpch_schema}

请基于以下基础模板生成完整的Python脚本：

{base_template}

**重要要求**：

1. **继承基础模板结构**：
   - 继承SQLiteExceptionTester类
   - 实现create_test_tables()、insert_test_data()、create_indexes()、execute_exception_tests()方法
   - 使用pass/sqlite3_logged作为SQLite可执行文件

2. **针对性测试设计**：
   - 根据{function_name}的风险点设计具体的测试场景
   - 在execute_exception_tests()方法中实现主要的异常触发逻辑
   - 考虑以下测试类型：
     * 事务冲突和死锁
     * 内存压力测试
     * I/O密集操作
     * 锁竞争
     * 缓存失效
     * 并发访问

3. **环境配置**：
   - 在configure_database()中添加针对性的PRAGMA设置
   - 在create_test_tables()中创建适合测试的表结构
   - 在insert_test_data()中插入足够的测试数据
   - 在create_indexes()中创建必要的索引

4. **代码质量**：
   - 代码结构清晰，注释详细
   - 错误处理完善
   - 输出信息有助于调试
   - 可以直接执行

5. **输出格式**：
   - 直接输出完整的Python代码
   - 不要包含任何解释文字或markdown格式
   - 代码应该可以直接保存为.py文件并执行

请生成针对 {function_name} 函数的完整异常测试脚本：
"""

        response = self.llm_client.generate_response(prompt)
        return response if response else None
    
    def save_generated_script(self, function_name, script_content):
        """保存生成的Python脚本"""
        if script_content:
            script_file = os.path.join(self.script_output_dir, f"{function_name}_test.py")
            with open(script_file, 'w', encoding='utf-8') as f:
                # 添加脚本头部注释
                f.write(f"#!/usr/bin/env python3\n")
                f.write(f'"""\n')
                f.write(f"Exception test script for SQLite function: {function_name}\n")
                f.write(f"Generated to trigger performance issues and exceptions\n")
                f.write(f"Uses pass/sqlite3_logged for testing\n")
                f.write(f'"""\n\n')
                f.write(script_content)

            # 设置脚本为可执行
            os.chmod(script_file, 0o755)
            print(f"为 {function_name} 生成了异常测试脚本: {script_file}")
    
    def generate_scripts_for_all_functions(self):
        """为所有函数生成异常测试脚本"""
        function_scores = self.load_function_scores()

        for function_name in function_scores.keys():
            print(f"为函数 {function_name} 生成异常测试脚本...")

            # 检查是否已经生成过
            script_file = os.path.join(self.script_output_dir, f"{function_name}_test.py")
            if os.path.exists(script_file):
                print(f"跳过已生成脚本的函数: {function_name}")
                continue

            risks = self.load_function_risks(function_name)

            if risks:
                script_content = self.generate_exception_script_for_function(function_name, risks)
                if script_content:
                    self.save_generated_script(function_name, script_content)
                else:
                    print(f"生成脚本失败: {function_name}")
            else:
                print(f"跳过无风险点的函数: {function_name}")

    def generate_single_script(self, function_name):
        """为单个函数生成异常测试脚本"""
        print(f"为函数 {function_name} 生成异常测试脚本...")

        risks = self.load_function_risks(function_name)

        if risks:
            script_content = self.generate_exception_script_for_function(function_name, risks)
            if script_content:
                self.save_generated_script(function_name, script_content)
                return True
            else:
                print(f"生成脚本失败: {function_name}")
                return False
        else:
            print(f"函数 {function_name} 无风险点")
            return False

if __name__ == "__main__":
    generator = ExceptionScriptGenerator()

    # 可以选择生成所有函数的脚本或单个函数的脚本
    import sys
    if len(sys.argv) > 1:
        # 生成指定函数的脚本
        function_name = sys.argv[1]
        generator.generate_single_script(function_name)
    else:
        # 生成所有函数的脚本
        generator.generate_scripts_for_all_functions()
