# SQLite异常测试脚本生成器

这个工具用于生成能够触发SQLite函数异常和性能问题的Python测试脚本。

## 功能特点

- 🎯 **针对性测试**: 基于函数风险点生成专门的异常测试脚本
- 🐍 **Python脚本**: 生成完整的Python脚本，而不是单个SQL语句
- 🔧 **环境配置**: 自动包含数据库配置、表创建、数据插入等环境准备
- 🚀 **即用性**: 生成的脚本可以直接运行，使用`pass/sqlite3_logged`进行测试
- 🧵 **并发测试**: 支持多线程并发操作，模拟真实的并发场景
- 📊 **结构化**: 基于模板生成，代码结构清晰，易于理解和修改

## 使用方法

### 1. 列出所有可用函数

```bash
python3 generator/run_script_generator.py --list
```

这会显示所有可用的SQLite函数及其风险点数量。

### 2. 为单个函数生成脚本

```bash
python3 generator/run_script_generator.py --function sqlite3BtreeBeginTrans
```

这会为指定函数生成异常测试脚本。

### 3. 为所有函数生成脚本

```bash
python3 generator/run_script_generator.py --all
```

这会为所有有风险点的函数生成异常测试脚本。

### 4. 指定输出目录

```bash
python3 generator/run_script_generator.py --all --output-dir my_test_scripts
```

## 生成的脚本结构

每个生成的脚本都包含以下部分：

### 1. 环境准备阶段
- **数据库配置**: PRAGMA设置、缓存大小、日志模式等
- **表创建**: 根据测试需要创建相应的表结构
- **数据插入**: 插入足够的测试数据
- **索引创建**: 创建必要的索引

### 2. 异常测试执行阶段
- **针对性测试**: 根据函数风险点设计的具体测试场景
- **并发操作**: 多线程并发执行，模拟锁竞争等场景
- **压力测试**: 内存压力、I/O密集操作等
- **事务控制**: 事务冲突、死锁等测试

### 3. 清理阶段
- **资源清理**: 删除测试数据库文件
- **状态重置**: 恢复初始状态

## 脚本示例

生成的脚本类似于：

```python
#!/usr/bin/env python3
"""
SQLite Function Exception Test Script
Generated to trigger specific performance issues and exceptions
"""

import os
import sys
import sqlite3
import subprocess
import time
import threading
from pathlib import Path

class SQLiteExceptionTester:
    def __init__(self):
        self.sqlite_path = "pass/sqlite3_logged"
        self.db_path = "test_exception.db"
        # ...

    def setup_environment(self):
        """环境准备阶段"""
        self.configure_database()
        self.create_test_tables()
        self.insert_test_data()
        self.create_indexes()

    def execute_exception_tests(self):
        """执行异常测试"""
        # 具体的测试逻辑
        pass

    def run(self):
        """运行测试"""
        try:
            self.setup_environment()
            self.execute_exception_tests()
        finally:
            self.cleanup()

if __name__ == "__main__":
    tester = SQLiteExceptionTester()
    tester.run()
```

## 运行生成的脚本

生成脚本后，可以直接运行：

```bash
# 运行单个测试脚本
python3 generated_scripts/sqlite3BtreeBeginTrans_test.py

# 或者使脚本可执行后直接运行
chmod +x generated_scripts/sqlite3BtreeBeginTrans_test.py
./generated_scripts/sqlite3BtreeBeginTrans_test.py
```

## 测试类型

生成的脚本可能包含以下类型的测试：

1. **事务冲突测试**: 多个并发事务操作同一数据
2. **内存压力测试**: 大量数据插入和查询
3. **I/O密集测试**: 强制缓存失效，增加磁盘I/O
4. **锁竞争测试**: 并发访问导致的锁竞争
5. **缓存失效测试**: 操作导致缓存频繁失效
6. **复杂查询测试**: 复杂的JOIN、子查询等操作

## 注意事项

1. **依赖**: 确保`pass/sqlite3_logged`文件存在且可执行
2. **权限**: 生成的脚本会自动设置为可执行权限
3. **清理**: 脚本会自动清理测试产生的临时文件
4. **超时**: 每个SQL操作都有超时保护，避免无限等待
5. **并发**: 并发测试可能会产生大量日志，注意磁盘空间

## 文件结构

```
generator/
├── generate_sql.py                 # 主生成器（已修改为脚本生成器）
├── exception_script_templates.py   # 脚本模板和工具函数
├── run_script_generator.py        # 运行脚本
├── README_SCRIPT_GENERATOR.md      # 本文档
├── function_risks/                 # 函数风险点定义
└── llm_client.py                  # LLM客户端

generated_scripts/                  # 生成的脚本输出目录
├── sqlite3BtreeBeginTrans_test.py
├── sqlite3PagerGet_test.py
└── ...
```

## 自定义和扩展

如果需要自定义生成的脚本，可以：

1. 修改`exception_script_templates.py`中的模板
2. 在`generate_sql.py`中调整提示词
3. 添加新的测试类型模板

这样可以生成更符合特定需求的异常测试脚本。
