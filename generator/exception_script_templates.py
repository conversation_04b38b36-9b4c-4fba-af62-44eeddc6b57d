#!/usr/bin/env python3
"""
异常测试脚本模板和工具函数
用于生成SQLite函数异常测试脚本
"""

class ScriptTemplates:
    """提供各种脚本模板和通用代码片段"""
    
    @staticmethod
    def get_base_template():
        """获取基础脚本模板"""
        return '''#!/usr/bin/env python3
"""
SQLite Function Exception Test Script
Generated to trigger specific performance issues and exceptions
"""

import os
import sys
import sqlite3
import subprocess
import time
import threading
from pathlib import Path

class SQLiteExceptionTester:
    def __init__(self):
        self.sqlite_path = "pass/sqlite3_logged"
        self.db_path = "test_exception.db"
        self.tpch_db_path = "pass/tpch.db"
        
        # 确保sqlite3_logged存在
        if not os.path.exists(self.sqlite_path):
            raise FileNotFoundError(f"SQLite executable not found: {self.sqlite_path}")
    
    def setup_environment(self):
        """环境准备阶段"""
        print("=== 环境准备阶段 ===")
        
        # 清理旧的测试数据库
        if os.path.exists(self.db_path):
            os.remove(self.db_path)
        
        # 数据库配置和初始化
        self.configure_database()
        self.create_test_tables()
        self.insert_test_data()
        self.create_indexes()
    
    def configure_database(self):
        """数据库配置调整"""
        print("配置数据库参数...")
        
        config_commands = [
            "PRAGMA cache_size = 2000;",
            "PRAGMA journal_mode = WAL;",
            "PRAGMA synchronous = NORMAL;",
            "PRAGMA temp_store = MEMORY;",
            "PRAGMA mmap_size = 268435456;",
        ]
        
        for cmd in config_commands:
            self.execute_sql_command(cmd)
    
    def create_test_tables(self):
        """创建测试表"""
        print("创建测试表...")
        # 子类需要实现具体的表创建逻辑
        pass
    
    def insert_test_data(self):
        """插入测试数据"""
        print("插入测试数据...")
        # 子类需要实现具体的数据插入逻辑
        pass
    
    def create_indexes(self):
        """创建索引"""
        print("创建索引...")
        # 子类需要实现具体的索引创建逻辑
        pass
    
    def execute_exception_tests(self):
        """执行异常测试"""
        print("=== 异常测试执行阶段 ===")
        # 子类需要实现具体的测试逻辑
        pass
    
    def execute_sql_command(self, sql_command, db_path=None):
        """执行SQL命令"""
        if db_path is None:
            db_path = self.db_path
            
        try:
            cmd = [self.sqlite_path, db_path, sql_command]
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            
            if result.returncode != 0:
                print(f"SQL执行错误: {result.stderr}")
                return False
            
            return True
        except subprocess.TimeoutExpired:
            print(f"SQL执行超时: {sql_command}")
            return False
        except Exception as e:
            print(f"执行SQL命令时发生异常: {e}")
            return False
    
    def execute_sql_file(self, sql_file_path, db_path=None):
        """执行SQL文件"""
        if db_path is None:
            db_path = self.db_path
            
        try:
            cmd = [self.sqlite_path, db_path, f".read {sql_file_path}"]
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
            
            if result.returncode != 0:
                print(f"SQL文件执行错误: {result.stderr}")
                return False
            
            return True
        except subprocess.TimeoutExpired:
            print(f"SQL文件执行超时: {sql_file_path}")
            return False
        except Exception as e:
            print(f"执行SQL文件时发生异常: {e}")
            return False
    
    def run_concurrent_operations(self, operations, num_threads=4):
        """并发执行操作"""
        print(f"启动 {num_threads} 个并发线程...")
        
        threads = []
        for i in range(num_threads):
            thread = threading.Thread(target=self.worker_thread, args=(operations, i))
            threads.append(thread)
            thread.start()
        
        for thread in threads:
            thread.join()
    
    def worker_thread(self, operations, thread_id):
        """工作线程"""
        print(f"线程 {thread_id} 开始执行...")
        
        for operation in operations:
            try:
                operation()
                time.sleep(0.1)  # 短暂延迟
            except Exception as e:
                print(f"线程 {thread_id} 执行操作时发生异常: {e}")
    
    def cleanup(self):
        """清理资源"""
        print("=== 清理阶段 ===")
        if os.path.exists(self.db_path):
            os.remove(self.db_path)
        print("清理完成")
    
    def run(self):
        """运行测试"""
        try:
            self.setup_environment()
            self.execute_exception_tests()
        except Exception as e:
            print(f"测试执行过程中发生异常: {e}")
        finally:
            self.cleanup()

def main():
    """主函数"""
    tester = SQLiteExceptionTester()
    tester.run()

if __name__ == "__main__":
    main()
'''
    
    @staticmethod
    def get_transaction_template():
        """获取事务相关的测试模板"""
        return '''
    def test_transaction_conflicts(self):
        """测试事务冲突"""
        print("测试事务冲突...")
        
        # 创建多个并发事务
        def transaction_operation():
            commands = [
                "BEGIN TRANSACTION;",
                "INSERT INTO test_table VALUES (1, 'test');",
                "UPDATE test_table SET value = 'updated' WHERE id = 1;",
                "COMMIT;"
            ]
            
            for cmd in commands:
                self.execute_sql_command(cmd)
                time.sleep(0.05)
        
        # 并发执行事务
        operations = [transaction_operation] * 10
        self.run_concurrent_operations(operations, num_threads=5)
'''
    
    @staticmethod
    def get_memory_pressure_template():
        """获取内存压力测试模板"""
        return '''
    def test_memory_pressure(self):
        """测试内存压力"""
        print("测试内存压力...")
        
        # 创建大量数据
        for i in range(10000):
            sql = f"INSERT INTO large_table VALUES ({i}, '{'x' * 1000}');"
            self.execute_sql_command(sql)
            
            if i % 1000 == 0:
                print(f"已插入 {i} 条记录")
'''
    
    @staticmethod
    def get_io_intensive_template():
        """获取I/O密集测试模板"""
        return '''
    def test_io_intensive_operations(self):
        """测试I/O密集操作"""
        print("测试I/O密集操作...")
        
        # 强制刷新缓存
        self.execute_sql_command("PRAGMA cache_size = 1;")
        
        # 执行大量随机访问
        for i in range(1000):
            random_id = i % 100
            sql = f"SELECT * FROM test_table WHERE id = {random_id};"
            self.execute_sql_command(sql)
'''
    
    @staticmethod
    def get_lock_contention_template():
        """获取锁竞争测试模板"""
        return '''
    def test_lock_contention(self):
        """测试锁竞争"""
        print("测试锁竞争...")
        
        def lock_operation():
            commands = [
                "BEGIN EXCLUSIVE TRANSACTION;",
                "SELECT * FROM test_table;",
                "UPDATE test_table SET value = 'locked';",
                "COMMIT;"
            ]
            
            for cmd in commands:
                self.execute_sql_command(cmd)
                time.sleep(0.1)
        
        # 并发执行锁操作
        operations = [lock_operation] * 8
        self.run_concurrent_operations(operations, num_threads=4)
'''
