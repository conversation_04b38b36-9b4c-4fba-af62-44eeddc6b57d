#!/usr/bin/env python3
"""
异常测试脚本生成器运行脚本
用于生成SQLite函数异常测试脚本
"""

import os
import sys
import argparse
from generate_sql import ExceptionScriptGenerator

def main():
    parser = argparse.ArgumentParser(description='生成SQLite函数异常测试脚本')
    parser.add_argument('--function', '-f', type=str, help='指定要生成脚本的函数名')
    parser.add_argument('--all', '-a', action='store_true', help='为所有函数生成脚本')
    parser.add_argument('--list', '-l', action='store_true', help='列出所有可用的函数')
    parser.add_argument('--output-dir', '-o', type=str, default='generated_scripts', 
                       help='输出目录 (默认: generated_scripts)')
    
    args = parser.parse_args()
    
    # 创建生成器实例
    generator = ExceptionScriptGenerator()
    generator.script_output_dir = args.output_dir
    
    # 确保输出目录存在
    os.makedirs(args.output_dir, exist_ok=True)
    
    if args.list:
        # 列出所有可用的函数
        list_available_functions(generator)
    elif args.function:
        # 生成指定函数的脚本
        generate_single_function(generator, args.function)
    elif args.all:
        # 生成所有函数的脚本
        generate_all_functions(generator)
    else:
        # 显示帮助信息
        parser.print_help()

def list_available_functions(generator):
    """列出所有可用的函数"""
    print("可用的SQLite函数列表：")
    print("=" * 50)
    
    try:
        function_scores = generator.load_function_scores()
        
        for i, function_name in enumerate(function_scores.keys(), 1):
            risks = generator.load_function_risks(function_name)
            risk_count = len(risks) if risks else 0
            
            print(f"{i:2d}. {function_name:<30} ({risk_count} 个风险点)")
            
            # 显示前3个风险点
            if risks:
                for j, risk in enumerate(risks[:3]):
                    print(f"    - {risk}")
                if len(risks) > 3:
                    print(f"    ... 还有 {len(risks) - 3} 个风险点")
            print()
            
    except Exception as e:
        print(f"加载函数列表时发生错误: {e}")

def generate_single_function(generator, function_name):
    """生成单个函数的脚本"""
    print(f"正在为函数 '{function_name}' 生成异常测试脚本...")
    
    try:
        success = generator.generate_single_script(function_name)
        
        if success:
            script_path = os.path.join(generator.script_output_dir, f"{function_name}_test.py")
            print(f"✓ 脚本生成成功: {script_path}")
            print(f"  运行命令: python3 {script_path}")
        else:
            print(f"✗ 脚本生成失败")
            
    except Exception as e:
        print(f"生成脚本时发生错误: {e}")

def generate_all_functions(generator):
    """生成所有函数的脚本"""
    print("正在为所有函数生成异常测试脚本...")
    
    try:
        function_scores = generator.load_function_scores()
        total_functions = len(function_scores)
        generated_count = 0
        skipped_count = 0
        failed_count = 0
        
        print(f"总共需要处理 {total_functions} 个函数")
        print("=" * 60)
        
        for i, function_name in enumerate(function_scores.keys(), 1):
            print(f"[{i}/{total_functions}] 处理函数: {function_name}")
            
            # 检查是否已经生成过
            script_file = os.path.join(generator.script_output_dir, f"{function_name}_test.py")
            if os.path.exists(script_file):
                print(f"  ⏭  跳过已生成的脚本")
                skipped_count += 1
                continue
            
            # 检查是否有风险点
            risks = generator.load_function_risks(function_name)
            if not risks:
                print(f"  ⚠  跳过无风险点的函数")
                skipped_count += 1
                continue
            
            # 生成脚本
            try:
                script_content = generator.generate_exception_script_for_function(function_name, risks)
                if script_content:
                    generator.save_generated_script(function_name, script_content)
                    print(f"  ✓  脚本生成成功")
                    generated_count += 1
                else:
                    print(f"  ✗  脚本生成失败 (LLM返回空内容)")
                    failed_count += 1
            except Exception as e:
                print(f"  ✗  脚本生成失败: {e}")
                failed_count += 1
        
        # 显示统计信息
        print("=" * 60)
        print(f"生成完成！统计信息：")
        print(f"  ✓ 成功生成: {generated_count} 个脚本")
        print(f"  ⏭ 跳过: {skipped_count} 个函数")
        print(f"  ✗ 失败: {failed_count} 个函数")
        print(f"  📁 输出目录: {generator.script_output_dir}")
        
        if generated_count > 0:
            print(f"\n运行示例:")
            print(f"  python3 {generator.script_output_dir}/<function_name>_test.py")
            
    except Exception as e:
        print(f"批量生成脚本时发生错误: {e}")

if __name__ == "__main__":
    main()
